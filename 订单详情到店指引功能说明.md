# 订单详情页到店指引功能实现说明

## 功能概述

在订单详情页面（`pages/user/order/order_detail`）的顶部添加了到店指引功能，为自提订单提供便捷的店铺信息和导航服务。

## 实现的功能

### 1. 店铺信息展示
- **店铺名称**：乙禾素食餐厅
- **店铺地址**：广州市黄埔区黄埔大道东976号港航中心二期B座1601房
- **视觉设计**：采用渐变背景的卡片式设计，美观且突出

### 2. 距离计算
- **自动获取**：页面加载时自动尝试获取用户位置并计算距离（需要位置权限）
- **手动获取**：提供"获取距离"按钮，用户可主动获取当前位置
- **距离显示**：以公里为单位显示到店距离，保留一位小数
- **权限处理**：未授权时引导用户开启位置权限

### 3. 自提时间信息
- **动态显示**：根据订单状态显示不同的自提时间信息
  - 待支付：显示"支付后可自提"
  - 已支付/已完成：显示"可随时自提（营业时间内）"
  - 默认：显示"营业时间：09:00-21:00"

### 4. 到店导航
- **一键导航**：点击"到店导航"按钮直接打开系统地图应用
- **精确定位**：使用店铺的精确经纬度坐标进行导航
- **错误处理**：导航失败时显示友好的错误提示

## 技术实现

### 文件修改

#### 1. WXML结构 (`order_detail.wxml`)
- 在订单详情顶部添加到店指引卡片
- 只在自提订单（`order.type === 'order'`）时显示
- 包含店铺信息、距离显示、自提时间和操作按钮

#### 2. JavaScript逻辑 (`order_detail.js`)
- 添加店铺信息数据（名称、地址、经纬度）
- 实现位置获取和距离计算功能
- 添加地图导航功能
- 根据订单状态动态设置自提时间信息

#### 3. 样式设计 (`order_detail.wxss`)
- 渐变背景的卡片设计
- 响应式布局适配不同屏幕
- 按钮交互效果和状态样式
- 与现有设计风格保持一致

### 核心功能方法

1. **`tryAutoGetDistance()`**：页面加载时自动尝试获取距离
2. **`getCurrentLocation()`**：手动获取当前位置并计算距离
3. **`calculateDistance()`**：使用球面距离公式计算两点间距离
4. **`navigateToStore()`**：打开系统地图应用进行导航

## 用户体验

### 界面设计
- 采用紫色渐变背景，视觉效果突出
- 卡片式布局，信息层次清晰
- 图标和文字搭配，直观易懂

### 交互体验
- 页面加载时自动获取距离（静默）
- 按钮状态反馈（加载中、禁用等）
- 权限引导和错误提示友好

### 功能完整性
- 支持距离计算和显示
- 支持一键导航到店
- 根据订单状态显示相应信息
- 完善的错误处理机制

## 使用场景

1. **用户下单后**：查看订单详情时可以了解店铺位置和距离
2. **准备自提时**：通过导航功能快速到达店铺
3. **了解自提时间**：根据订单状态了解何时可以自提

## 注意事项

1. **位置权限**：需要用户授权位置权限才能计算距离
2. **网络依赖**：地图导航功能需要网络连接
3. **兼容性**：使用微信小程序标准API，兼容性良好
4. **性能优化**：距离计算使用本地算法，响应快速

## 后续优化建议

1. 添加店铺营业状态实时显示
2. 支持多个店铺位置选择
3. 添加预计到达时间计算
4. 集成实时路况信息
