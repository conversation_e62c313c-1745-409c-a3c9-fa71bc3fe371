// pages/delivery/delivery.js
import { loginRequest } from '../../service/index';

Page({
  data: {
    loading: true,
    categories: [],
    currentCategory: '0',
    dishes: [],
    currentCategoryDishes: [],
    cartItems: [],
    cartTotal: {
      count: 0,
      price: 0
    },
    cartVisible: false,
    showDishModal: false,
    selectedDish: {},
    pageNo: 1,
    pageSize: 20,
    hasMore: true,
    showCouponList: false,
    selectedCoupons: [],
    selectedCouponIds: [],
    products: [],
    showCheckout: false,
    couponDiscount: 0,
    finalAmount: 0,
    orderSuccess: false,
    orderId: '',
    // 支付相关数据
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [],
    selectedEnterprise: null,
    source: 'delivery',
    // 配送相关数据
    deliveryAddress: null,
    selectedTimeSlot: null,
    timeSlots: [],
    deliveryFee: 0,
    showAddressModal: false,
    showTimeModal: false
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '外卖服务'
    });

    // 处理插件返回的地址数据
    if (options.payload) {
      try {
        const addressData = JSON.parse(options.payload);
        this.handlePluginAddressData(addressData);
      } catch (error) {
        console.error('解析插件地址数据失败:', error);
      }
    }

    this.fetchCategories();
    this.generateTimeSlots();
  },

  onPullDownRefresh: function () {
    this.setData({
      pageNo: 1,
      hasMore: true
    });
    this.fetchDishes(this.data.currentCategory);
    wx.stopPullDownRefresh();
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        pageNo: this.data.pageNo + 1
      });
      this.fetchDishes(this.data.currentCategory, true);
    }
  },

  /**
   * 获取商品分类
   */
  fetchCategories() {
    this.setData({ loading: true });

    loginRequest.get({
      url: '/menu/categories/search',
      data: {
        key: 'delivery',
        show_root: false
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200 && res.data && res.data.length > 0) {
        const categories = res.data || [];
        this.setData({
          categories,
          currentCategory: categories[0]?.id || '0',
          loading: false
        });

        // 获取第一个分类的菜品
        this.fetchDishes(categories[0]?.id || '0');
      } else {
        this.setData({
          categories: [],
          loading: false
        });
        // 如果没有分类，仍然尝试获取菜品
        this.fetchDishes('0');
      }
    }).catch(err => {
      console.error('获取分类失败', err);
      this.setData({
        categories: [],
        loading: false
      });
      this.fetchDishes('0');
    });
  },

  /**
   * 获取菜品列表
   */
  fetchDishes(categoryId, append = false) {
    const { pageNo, pageSize } = this.data;

    this.setData({
      loading: !append
    });

    // 从全局配置中获取商店ID
    const app = getApp();
    const storeId = app.globalData.defaultStoreId || 1;

    loginRequest.get({
      url: '/menu/dishes',
      data: {
        store_id: storeId,
        category_id: categoryId,
        page_no: pageNo,
        page_size: pageSize
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const newDishes = res.data.list || [];
        const hasMore = newDishes.length === pageSize;

        // 处理菜品数据，添加购物车中的数量
        newDishes.forEach(dish => {
          const cartItem = this.data.cartItems.find(item => item.id === dish.id);
          dish.count = cartItem ? cartItem.count : 0;
        });

        if (append) {
          // 加载更多
          const updatedDishes = [...this.data.currentCategoryDishes, ...newDishes];
          this.setData({
            currentCategoryDishes: updatedDishes,
            hasMore,
            loading: false
          });
        } else {
          // 重新加载
          this.setData({
            dishes: newDishes,
            currentCategoryDishes: newDishes,
            pageNo: 2,
            hasMore,
            loading: false
          });
        }
      } else {
        this.setData({
          loading: false
        });
        if (!append) {
          this.setData({
            currentCategoryDishes: []
          });
        }

        // 显示错误提示
        if (res.message) {
          wx.showToast({
            title: res.message,
            icon: 'none'
          });
        }
      }
    }).catch(err => {
      console.error('获取菜品失败', err);
      this.setData({
        loading: false
      });
      if (!append) {
        this.setData({
          currentCategoryDishes: []
        });
      }

      // 显示网络错误提示
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 选择分类
   */
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      currentCategory: categoryId,
      pageNo: 1,
      hasMore: true
    });
    this.fetchDishes(categoryId);
  },

  /**
   * 添加菜品到购物车
   */
  addDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1);
  },

  /**
   * 从购物车减少菜品
   */
  minusDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1);
  },

  /**
   * 在弹窗中添加菜品
   */
  addDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1, true);
  },

  /**
   * 在弹窗中减少菜品
   */
  minusDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1, true);
  },

  /**
   * 更新购物车
   */
  updateCart(dishId, change, isModal = false) {
    // 查找菜品
    const dish = this.data.dishes.find(item => item.id === dishId) ||
                 this.data.currentCategoryDishes.find(item => item.id === dishId);
    if (!dish) return;

    // 更新菜品计数
    const oldCount = dish.count || 0;
    const newCount = Math.max(0, oldCount + change);
    dish.count = newCount;

    // 更新所有菜品列表中的计数
    const dishes = this.data.dishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    // 更新购物车
    let cartItems = [...this.data.cartItems];

    if (newCount > 0) {
      const cartItemIndex = cartItems.findIndex(item => item.id === dishId);
      if (cartItemIndex >= 0) {
        cartItems[cartItemIndex].count = newCount;
      } else {
        cartItems.push({
          id: dish.id,
          name: dish.name,
          price: dish.price,
          count: newCount
        });
      }
    } else {
      cartItems = cartItems.filter(item => item.id !== dishId);
    }

    // 计算购物车总数和总价
    const cartTotal = this.calculateCartTotal(cartItems);

    // 更新选中的菜品（如果在弹窗中）
    const updateData = {
      dishes,
      currentCategoryDishes,
      cartItems,
      cartTotal
    };

    // 如果是在弹窗中操作，同时更新选中的菜品
    if (isModal) {
      updateData.selectedDish = { ...dish };
    }

    this.setData(updateData);
  },

  /**
   * 计算购物车总数和总价
   */
  calculateCartTotal(cartItems) {
    let count = 0;
    let price = 0;

    cartItems.forEach(item => {
      count += item.count;
      price += item.price * item.count;
    });

    return {
      count,
      price: parseFloat(price.toFixed(2))
    };
  },

  /**
   * 切换购物车展开/收起状态
   */
  toggleCart() {
    if (this.data.cartTotal.count > 0) {
      this.setData({
        cartVisible: !this.data.cartVisible
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: '提示',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          // 重置所有菜品的count
          const dishes = this.data.dishes.map(item => ({ ...item, count: 0 }));
          const currentCategoryDishes = this.data.currentCategoryDishes.map(item => ({ ...item, count: 0 }));

          this.setData({
            dishes,
            currentCategoryDishes,
            cartItems: [],
            cartTotal: { count: 0, price: 0 },
            cartVisible: false
          });
        }
      }
    });
  },

  /**
   * 显示菜品详情
   */
  showDishDetail(e) {
    const dish = e.currentTarget.dataset.dish;
    this.setData({
      selectedDish: dish,
      showDishModal: true
    });
  },

  /**
   * 隐藏菜品详情
   */
  hideDishDetail() {
    this.setData({
      showDishModal: false,
      selectedDish: {}
    });
  },

  /**
   * 地址变化事件处理
   */
  onAddressChange(e) {
    const selectedAddress = e.detail;
    this.setData({
      deliveryAddress: selectedAddress
    });
  },

  /**
   * 处理插件返回的地址数据
   */
  handlePluginAddressData(data) {
    console.log('处理插件地址数据:', data);

    // 获取地址管理组件
    const addressManager = this.selectComponent('#address-manager');
    if (addressManager && addressManager.savePluginAddress) {
      addressManager.savePluginAddress(data).then(() => {
        wx.showToast({
          title: '地址保存成功',
          icon: 'success'
        });
      }).catch(error => {
        console.error('保存地址失败:', error);
        wx.showToast({
          title: '保存地址失败',
          icon: 'none'
        });
      });
    } else {
      // 备用保存方法
      this.saveAddressDirectly(data);
    }
  },

  /**
   * 直接保存地址数据
   */
  saveAddressDirectly(data) {
    try {
      const savedAddresses = wx.getStorageSync('savedAddresses') || [];
      const newAddress = {
        id: Date.now().toString(),
        name: data.receiver,
        phone: data.phone,
        province: data.area && data.area[0] ? data.area[0].name : '广东省',
        city: data.area && data.area[1] ? data.area[1].name : '广州市',
        district: data.area && data.area[2] ? data.area[2].name : '黄埔区',
        detail: data.address,
        label: data.label || '',
        isDefault: data.default || savedAddresses.length === 0,
        latitude: null,
        longitude: null
      };

      // 如果设置为默认地址，先取消其他地址的默认状态
      if (newAddress.isDefault) {
        savedAddresses.forEach(addr => addr.isDefault = false);
      }

      savedAddresses.push(newAddress);
      wx.setStorageSync('savedAddresses', savedAddresses);

      // 如果是默认地址，设置为当前配送地址
      if (newAddress.isDefault) {
        this.setData({
          deliveryAddress: newAddress
        });
      }

      wx.showToast({
        title: '地址保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('直接保存地址失败:', error);
      wx.showToast({
        title: '保存地址失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成时间段
   */
  generateTimeSlots() {
    const timeSlots = [
      { id: '1', label: '09:00-12:00', value: '09:00-12:00' },
      { id: '2', label: '12:00-15:00', value: '12:00-15:00' },
      { id: '3', label: '15:00-18:00', value: '15:00-18:00' },
      { id: '4', label: '18:00-21:00', value: '18:00-21:00' }
    ];
    this.setData({
      timeSlots
    });
  },



  /**
   * 获取当前位置
   */
  getCurrentLocation() {
    wx.showLoading({
      title: '获取位置中...'
    });

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        // 调用逆地理编码获取地址信息
        this.reverseGeocode(res.latitude, res.longitude);
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取位置失败', err);

        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '位置权限',
            content: '需要获取您的位置信息来自动填写地址，请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 逆地理编码（使用腾讯地图API）
   */
  reverseGeocode(latitude, longitude) {
    wx.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      data: {
        location: `${latitude},${longitude}`,
        key: 'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23', // 腾讯地图API密钥
        get_poi: 1
      },
      success: (res) => {
        wx.hideLoading();

        if (res.data.status === 0) {
          const result = res.data.result;
          const addressComponent = result.address_component;

          // 位置获取成功，但地址表单现在由address-manager组件处理
          wx.showToast({
            title: '位置获取成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '地址解析失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },





  /**
   * 显示时间选择
   */
  showTimeSelector() {
    this.setData({
      showTimeModal: true
    });
  },

  /**
   * 隐藏时间选择
   */
  hideTimeSelector() {
    this.setData({
      showTimeModal: false
    });
  },

  /**
   * 选择时间段
   */
  selectTimeSlot(e) {
    const timeSlot = e.currentTarget.dataset.slot;
    this.setData({
      selectedTimeSlot: timeSlot,
      showTimeModal: false
    });
  },

  /**
   * 生成产品列表
   */
  generateProductList() {
    return this.data.cartItems.map(item => ({
      product_id: item.id,
      quantity: item.count
    }));
  },

  /**
   * 去结算
   */
  goToCheckout() {
    if (this.data.cartTotal.count === 0) {
      wx.showToast({
        title: '请先选择商品',
        icon: 'none'
      });
      return;
    }

    if (!this.data.deliveryAddress) {
      wx.showToast({
        title: '请先选择配送地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedTimeSlot) {
      wx.showToast({
        title: '请先选择配送时间',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCheckout: true,
      finalAmount: this.data.cartTotal.price + this.data.deliveryFee
    });
  },

  /**
   * 隐藏结算页面
   */
  hideCheckout() {
    this.setData({
      showCheckout: false
    });
  },

  /**
   * 显示优惠券列表
   */
  showCouponList() {
    this.setData({
      showCouponList: true,
      products: this.generateProductList()
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 调用后端API计算准确的优惠金额
      this.calculateCouponPricing(couponIds, coupons);
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  /**
   * 计算优惠券价格
   */
  calculateCouponPricing(couponIds, coupons) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败',
        icon: 'none'
      });
      return;
    }

    const products = this.generateProductList();

    if (!couponIds || couponIds.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });
      return;
    }

    const requestData = {
      user_id: userInfo.id,
      products: products,
      coupon_usage_record_ids: couponIds
    };

    wx.showLoading({
      title: '计算优惠中...'
    });

    wx.request({
      url: `${app.globalData.baseUrl}/coupon/coupon-pricing`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'token': wx.getStorageSync('token')
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();

        if (res.data && res.data.status === 200) {
          const data = res.data.data;
          const pricingResult = data.pricing_result;

          if (pricingResult && pricingResult.discount) {
            const totalDiscount = pricingResult.discount.total_discount || 0;
            const discountCoupons = pricingResult.discount.coupons || [];

            const couponsWithDiscount = coupons.map(coupon => {
              const discountInfo = discountCoupons.find(dc =>
                dc.coupon_usage_record_id === coupon.coupon_usage_record_id
              );

              return {
                ...coupon,
                discountAmount: discountInfo ? discountInfo.discount_amount : 0
              };
            });

            const finalAmount = Math.max(0, this.data.cartTotal.price - totalDiscount);

            this.setData({
              selectedCoupons: couponsWithDiscount,
              selectedCouponIds: couponIds,
              couponDiscount: totalDiscount,
              finalAmount: finalAmount,
              showCouponList: false
            });

            wx.showToast({
              title: `已选择${coupons.length}张优惠券，优惠¥${totalDiscount}`,
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '计算优惠失败',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: res.data?.message || '计算优惠失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('计算优惠券价格失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    // 检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=delivery'
      });
      return;
    }

    this.createDeliveryOrder();
  },

  /**
   * 创建配送订单
   */
  createDeliveryOrder() {
    wx.showLoading({
      title: '提交中...'
    });

    // 准备订单数据
    const orderItems = this.data.cartItems.map(item => ({
      dish_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));

    const orderData = {
      order_type: 'delivery',
      items: orderItems,
      total_amount: this.data.finalAmount || this.data.cartTotal.price,
      delivery_address: this.data.deliveryAddress,
      delivery_time: this.data.selectedTimeSlot,
      delivery_fee: this.data.deliveryFee
    };

    console.log('提交配送订单数据:', orderData);

    loginRequest.post({
      url: '/delivery-order/create',
      data: orderData,
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      wx.hideLoading();

      console.log('配送订单创建响应:', JSON.stringify(res));

      if (res.order_no) {
        this.setData({
          showCheckout: false,
          orderSuccess: true,
          orderId: res.order_no
        });

        // 清空购物车
        this.clearCartAfterOrder();

        wx.showToast({
          title: '订单提交成功',
          icon: 'success',
          duration: 2000
        });
      } else if (res.status === 401) {
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=delivery'
        });
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('请求失败', err);
      wx.hideLoading();

      wx.showToast({
        title: err.message || '网络错误，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 订单成功后清空购物车
   */
  clearCartAfterOrder() {
    // 重置所有菜品的count
    const dishes = this.data.dishes.map(item => ({ ...item, count: 0 }));
    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => ({ ...item, count: 0 }));

    this.setData({
      dishes,
      currentCategoryDishes,
      cartItems: [],
      cartTotal: { count: 0, price: 0 },
      cartVisible: false
    });
  },

  /**
   * 跳转到订单详情
   */
  goToOrderDetail() {
    wx.switchTab({
      url: '/pages/reserve/reserve?tab=all'
    });
  }
});
