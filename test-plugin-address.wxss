/* 测试页面样式 */
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  margin-bottom: 60rpx;
}

.test-item {
  margin-bottom: 30rpx;
}

.test-btn {
  width: 100%;
  padding: 30rpx;
  background: #007aff;
  color: #fff;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
}

.test-btn:active {
  background: #0056cc;
}

.result-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #007aff;
  padding-bottom: 15rpx;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 150rpx;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
