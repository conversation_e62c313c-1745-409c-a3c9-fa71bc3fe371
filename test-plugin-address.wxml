<!-- 测试地图地址输入服务插件 -->
<view class="container">
  <view class="header">
    <text class="title">地图地址输入服务插件测试</text>
  </view>

  <view class="test-section">
    <view class="test-item">
      <button class="test-btn" bindtap="testAddAddress">测试新增地址</button>
    </view>
    
    <view class="test-item">
      <button class="test-btn" bindtap="testEditAddress">测试编辑地址</button>
    </view>
  </view>

  <view class="result-section" wx:if="{{testAddress}}">
    <view class="result-title">插件返回的地址数据：</view>
    <view class="result-content">
      <view class="result-item">
        <text class="label">收件人：</text>
        <text class="value">{{testAddress.receiver}}</text>
      </view>
      <view class="result-item">
        <text class="label">手机号：</text>
        <text class="value">{{testAddress.phone}}</text>
      </view>
      <view class="result-item">
        <text class="label">地区：</text>
        <text class="value">{{testAddress.area[0].name}} {{testAddress.area[1].name}} {{testAddress.area[2].name}}</text>
      </view>
      <view class="result-item">
        <text class="label">详细地址：</text>
        <text class="value">{{testAddress.address}}</text>
      </view>
      <view class="result-item">
        <text class="label">标签：</text>
        <text class="value">{{testAddress.label}}</text>
      </view>
      <view class="result-item">
        <text class="label">默认地址：</text>
        <text class="value">{{testAddress.default ? '是' : '否'}}</text>
      </view>
    </view>
  </view>
</view>
