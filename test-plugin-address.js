// 测试地图地址输入服务插件
Page({
  data: {
    testAddress: null
  },

  onLoad() {
    console.log('测试页面加载');
  },

  // 测试新增地址
  testAddAddress() {
    wx.navigateTo({ 
      url: 'plugin://address-form/index' 
    });
  },

  // 测试编辑地址
  testEditAddress() {
    const editInfo = {
      receiver: '张三',
      phone: '13800138000',
      area: [
        { name: '广东省', id: '440000' },
        { name: '广州市', id: '440100' },
        { name: '天河区', id: '440106' }
      ],
      address: '天河路123号',
      label: '家',
      default: true
    };

    wx.navigateTo({ 
      url: `plugin://address-form/index?edit=${JSON.stringify(editInfo)}` 
    });
  },

  // 处理插件返回的地址数据
  handlePluginAddress(data) {
    console.log('插件返回的地址数据:', data);
    this.setData({
      testAddress: data
    });
    
    wx.showToast({
      title: '地址保存成功',
      icon: 'success'
    });
  }
});
