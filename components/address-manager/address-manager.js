Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的地址
    selectedAddress: {
      type: Object,
      value: null
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请选择配送地址'
    },
    // 是否显示地址选择器
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 地址管理相关
    savedAddresses: [],
    showAddressList: false,
    showAddressForm: false,
    editingAddress: null,
    addressForm: {
      id: null,
      name: '',
      phone: '',
      province: '广东省',
      city: '广州市',
      district: '黄埔区',
      detail: '',
      isDefault: false,
      latitude: null,
      longitude: null
    },
    // 地图选择相关
    showMapSelector: false,
    mapCenter: {
      latitude: 23.0959878,  // 默认广州坐标
      longitude: 113.431943
    },
    mapScale: 16,
    mapMarkers: [],
    // 广州市区县列表
    guangzhouDistricts: [
      '越秀区', '荔湾区', '海珠区', '天河区', '白云区', '黄埔区',
      '番禺区', '花都区', '南沙区', '从化区', '增城区'
    ],
    // 当前选择的区域索引
    selectedDistrictIndex: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 加载用户地址
     */
    loadUserAddress() {
      // 从本地存储获取保存的地址列表
      const savedAddresses = wx.getStorageSync('savedAddresses') || [];
      // 查找默认地址
      const defaultAddress = savedAddresses.find(addr => addr.isDefault) || savedAddresses[0] || null;

      this.setData({
        savedAddresses
      });

      // 如果没有选中地址且有默认地址，则设置默认地址
      if (!this.data.selectedAddress && defaultAddress) {
        this.setData({
          selectedAddress: defaultAddress
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', defaultAddress);
      }
    },

    /**
     * 使用插件新增地址
     */
    addAddressWithPlugin() {
      wx.navigateTo({
        url: 'plugin://address-form/index'
      });
    },

    /**
     * 使用插件编辑地址
     */
    editAddressWithPlugin(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (address) {
        // 转换地址格式为插件需要的格式
        const editInfo = {
          receiver: address.name,
          phone: address.phone,
          area: [
            { name: address.province, id: '' },
            { name: address.city, id: '' },
            { name: address.district, id: '' }
          ],
          address: address.detail,
          label: address.label || '',
          default: address.isDefault
        };

        wx.navigateTo({
          url: `plugin://address-form/index?edit=${JSON.stringify(editInfo)}`
        });
      }
    },

    /**
     * 保存插件返回的地址数据
     */
    savePluginAddress(data) {
      return new Promise((resolve, reject) => {
        try {
          console.log('保存插件地址数据:', data);

          // 验证必要字段
          if (!data.receiver || !data.phone || !data.address) {
            throw new Error('地址信息不完整');
          }

          // 转换插件数据格式为本地格式
          const newAddress = {
            id: Date.now().toString(),
            name: data.receiver,
            phone: data.phone,
            province: data.area && data.area[0] ? data.area[0].name : '广东省',
            city: data.area && data.area[1] ? data.area[1].name : '广州市',
            district: data.area && data.area[2] ? data.area[2].name : '黄埔区',
            detail: data.address,
            label: data.label || '',
            isDefault: data.default || false,
            latitude: null, // 插件可能不提供经纬度
            longitude: null
          };

          let savedAddresses = [...this.data.savedAddresses];

          // 如果设置为默认地址，先取消其他地址的默认状态
          if (newAddress.isDefault) {
            savedAddresses = savedAddresses.map(addr => ({ ...addr, isDefault: false }));
          }

          // 如果是第一个地址，自动设为默认
          if (savedAddresses.length === 0) {
            newAddress.isDefault = true;
          }

          // 添加新地址
          savedAddresses.push(newAddress);

          this.setData({
            savedAddresses: savedAddresses
          });

          // 保存到本地存储
          this.saveAddressesToStorage();

          // 如果是默认地址或第一个地址，设置为当前选中地址
          if (newAddress.isDefault || savedAddresses.length === 1) {
            this.setData({
              selectedAddress: newAddress
            });
            // 通知父组件地址变化
            this.triggerEvent('addresschange', newAddress);
          }

          console.log('地址保存成功:', newAddress);
          resolve(newAddress);
        } catch (error) {
          console.error('保存地址失败:', error);
          reject(error);
        }
      });
    },

    /**
     * 保存地址到本地存储
     */
    saveAddressesToStorage() {
      wx.setStorageSync('savedAddresses', this.data.savedAddresses);
    },

    /**
     * 显示地址选择
     */
    showAddressSelector() {
      this.setData({
        showAddressList: true
      });
    },

    /**
     * 隐藏地址选择
     */
    hideAddressSelector() {
      this.setData({
        showAddressList: false,
        showAddressForm: false
      });
    },

    /**
     * 选择地址
     */
    selectAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const selectedAddress = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (selectedAddress) {
        this.setData({
          selectedAddress: selectedAddress,
          showAddressList: false
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', selectedAddress);
      }
    },

    /**
     * 显示新增地址表单
     */
    showAddAddressForm() {
      this.setData({
        showAddressForm: true,
        editingAddress: null,
        addressForm: {
          id: null,
          name: '',
          phone: '',
          province: '广东省',
          city: '广州市',
          district: '黄埔区',
          detail: '',
          isDefault: this.data.savedAddresses.length === 0, // 如果是第一个地址，默认设为默认地址
          latitude: null,
          longitude: null
        },
        selectedDistrictIndex: 0
      });
    },

    /**
     * 显示地图选择器
     */
    showMapLocationSelector() {
      console.log('显示地图选择器');
      // 先检查位置权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接显示地图
            this.initMapLocation();
          } else {
            // 未授权，请求授权
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                // 授权成功，显示地图
                this.initMapLocation();
              },
              fail: () => {
                wx.showModal({
                  title: '位置权限',
                  content: '需要获取您的位置信息来使用地图选择功能，请在设置中开启位置权限',
                  showCancel: true,
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              }
            });
          }
        },
        fail: () => {
          wx.showToast({
            title: '获取权限信息失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 初始化地图位置
     */
    initMapLocation() {
      console.log('初始化地图位置');
      wx.showLoading({ title: '获取位置中...' });

      // 获取当前位置作为地图中心点
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          wx.hideLoading();
          this.setData({
            showMapSelector: true,
            mapCenter: {
              latitude: res.latitude,
              longitude: res.longitude
            },
            mapMarkers: [{
              id: 1,
              latitude: res.latitude,
              longitude: res.longitude,
              iconPath: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTAiIGZpbGw9IiNGRjRBNEEiLz4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iNSIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
              width: 30,
              height: 30,
              anchor: { x: 0.5, y: 0.5 }
            }]
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('获取位置失败:', err);
          // 即使获取位置失败，也显示地图（使用默认位置）
          this.setData({
            showMapSelector: true,
            mapMarkers: [{
              id: 1,
              latitude: this.data.mapCenter.latitude,
              longitude: this.data.mapCenter.longitude,
              iconPath: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTAiIGZpbGw9IiNGRjRBNEEiLz4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iNSIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
              width: 30,
              height: 30,
              anchor: { x: 0.5, y: 0.5 }
            }]
          });
          wx.showToast({
            title: '使用默认位置，请在地图上选择',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    /**
     * 隐藏地图选择器
     */
    hideMapSelector() {
      this.setData({
        showMapSelector: false
      });
    },

    /**
     * 地图点击事件
     */
    onMapTap(e) {
      const { latitude, longitude } = e.detail;

      // 更新地图中心和标记点
      this.setData({
        mapCenter: { latitude, longitude },
        mapMarkers: [{
          id: 1,
          latitude,
          longitude,
          iconPath: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTAiIGZpbGw9IiNGRjRBNEEiLz4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iNSIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
          width: 30,
          height: 30,
          anchor: { x: 0.5, y: 0.5 }
        }]
      });
    },

    /**
     * 地图视野变化事件
     */
    onMapRegionChange(e) {
      if (e.detail.type === 'end') {
        const { centerLocation } = e.detail;
        if (centerLocation) {
          // 更新标记点位置到地图中心
          this.setData({
            mapCenter: {
              latitude: centerLocation.latitude,
              longitude: centerLocation.longitude
            },
            mapMarkers: [{
              id: 1,
              latitude: centerLocation.latitude,
              longitude: centerLocation.longitude,
              iconPath: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iMTAiIGZpbGw9IiNGRjRBNEEiLz4KPGNpcmNsZSBjeD0iMTUiIGN5PSIxNSIgcj0iNSIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
              width: 30,
              height: 30,
              anchor: { x: 0.5, y: 0.5 }
            }]
          });
        }
      }
    },

    /**
     * 确认地图选择的位置
     */
    confirmMapLocation() {
      const { latitude, longitude } = this.data.mapCenter;

      wx.showLoading({ title: '解析地址中...' });

      // 先保存经纬度到地址表单
      this.setData({
        'addressForm.latitude': latitude,
        'addressForm.longitude': longitude
      });

      // 调用逆地址解析
      this.reverseGeocode(latitude, longitude);

      // 隐藏地图选择器
      this.setData({
        showMapSelector: false
      });
    },

    /**
     * 获取当前位置（保留原有方法作为备用）
     */
    getCurrentLocation() {
      wx.showLoading({ title: '获取位置中...' });

      // 先检查位置权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接获取位置
            this.getLocationData();
          } else {
            // 未授权，请求授权
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                // 授权成功，获取位置
                this.getLocationData();
              },
              fail: () => {
                wx.hideLoading();
                wx.showModal({
                  title: '位置权限',
                  content: '需要获取您的位置信息来自动填充地址，请在设置中开启位置权限',
                  showCancel: true,
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              }
            });
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '获取权限信息失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 获取位置数据
     */
    getLocationData() {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 调用腾讯地图逆地址解析API
          this.reverseGeocode(res.latitude, res.longitude);
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('获取位置失败:', err);
          wx.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    /**
     * 逆地址解析
     */
    reverseGeocode(lat, lng) {
      const key = 'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23'; // 腾讯地图API密钥
      
      wx.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: `${lat},${lng}`,
          key: key,
          get_poi: 1
        },
        success: (res) => {
          wx.hideLoading();
          if (res.data.status === 0) {
            const result = res.data.result;
            const addressComponent = result.address_component;

            // 如果解析出的地址在广州市，则使用解析结果，否则默认设置为广州市
            let province = '广东省';
            let city = '广州市';
            let district = this.data.guangzhouDistricts[0]; // 默认越秀区
            let selectedIndex = 0;

            if (addressComponent.province === '广东省' && addressComponent.city === '广州市') {
              // 检查区是否在广州市区列表中
              const districtIndex = this.data.guangzhouDistricts.indexOf(addressComponent.district);
              if (districtIndex !== -1) {
                district = addressComponent.district;
                selectedIndex = districtIndex;
              }
            }

            this.setData({
              'addressForm.province': province,
              'addressForm.city': city,
              'addressForm.district': district,
              'addressForm.detail': result.formatted_addresses?.recommend || result.address,
              'addressForm.latitude': lat,
              'addressForm.longitude': lng,
              selectedDistrictIndex: selectedIndex
            });

            wx.showToast({
              title: '地址解析成功',
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '地址解析失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('逆地址解析失败:', err);
          wx.showToast({
            title: '地址解析失败',
            icon: 'none'
          });
        }
      });
    },

    /**
     * 显示编辑地址表单
     */
    showEditAddressForm(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (address) {
        // 查找当前地址的区在广州市区列表中的索引
        let districtIndex = 0;
        if (address.province === '广东省' && address.city === '广州市') {
          const index = this.data.guangzhouDistricts.indexOf(address.district);
          if (index !== -1) {
            districtIndex = index;
          }
        }

        this.setData({
          showAddressForm: true,
          editingAddress: address,
          addressForm: {
            ...address,
            province: '广东省',
            city: '广州市',
            district: address.district || '黄埔区',
            latitude: address.latitude || null,
            longitude: address.longitude || null
          },
          selectedDistrictIndex: districtIndex
        });
      }
    },

    /**
     * 隐藏地址表单
     */
    hideAddressForm() {
      this.setData({
        showAddressForm: false,
        editingAddress: null
      });
    },

    /**
     * 表单输入处理
     */
    onAddressFormInput(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;

      this.setData({
        [`addressForm.${field}`]: value
      });
    },

    /**
     * 切换默认地址
     */
    toggleDefaultAddress(e) {
      const isDefault = e.detail.value;
      this.setData({
        'addressForm.isDefault': isDefault
      });
    },

    /**
     * 选择地区（限制为广州市的区）
     */
    onDistrictChange(e) {
      const selectedIndex = e.detail.value;
      const selectedDistrict = this.data.guangzhouDistricts[selectedIndex];

      this.setData({
        selectedDistrictIndex: selectedIndex,
        'addressForm.province': '广东省',
        'addressForm.city': '广州市',
        'addressForm.district': selectedDistrict
      });
    },

    /**
     * 选择地区（保留原方法作为备用）
     */
    onRegionChange(e) {
      const [province, city, district] = e.detail.value;
      this.setData({
        'addressForm.province': province,
        'addressForm.city': city,
        'addressForm.district': district
      });
    },

    /**
     * 保存地址
     */
    saveAddress() {
      const { addressForm, savedAddresses, editingAddress } = this.data;

      // 表单验证
      if (!addressForm.name.trim()) {
        wx.showToast({ title: '请输入收货人姓名', icon: 'none' });
        return;
      }

      if (!addressForm.phone.trim()) {
        wx.showToast({ title: '请输入手机号码', icon: 'none' });
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(addressForm.phone)) {
        wx.showToast({ title: '请输入正确的手机号码', icon: 'none' });
        return;
      }

      if (!addressForm.province || !addressForm.city || !addressForm.district) {
        wx.showToast({ title: '请选择所在地区', icon: 'none' });
        return;
      }

      if (!addressForm.detail.trim()) {
        wx.showToast({ title: '请输入详细地址', icon: 'none' });
        return;
      }

      let newAddresses = [...savedAddresses];

      // 如果设置为默认地址，先取消其他地址的默认状态
      if (addressForm.isDefault) {
        newAddresses = newAddresses.map(addr => ({ ...addr, isDefault: false }));
      }

      if (editingAddress) {
        // 编辑现有地址
        const index = newAddresses.findIndex(addr => addr.id === editingAddress.id);
        if (index !== -1) {
          newAddresses[index] = { ...addressForm };
        }
      } else {
        // 新增地址
        const newAddress = {
          ...addressForm,
          id: Date.now().toString() // 简单的ID生成
        };
        newAddresses.push(newAddress);
      }

      this.setData({
        savedAddresses: newAddresses,
        showAddressForm: false,
        editingAddress: null
      });

      // 保存到本地存储
      this.saveAddressesToStorage();

      // 如果是默认地址或者是第一个地址，设置为当前选中地址
      if (addressForm.isDefault || newAddresses.length === 1) {
        const savedAddress = editingAddress ?
          newAddresses.find(addr => addr.id === editingAddress.id) :
          newAddresses[newAddresses.length - 1];

        this.setData({
          selectedAddress: savedAddress,
          showAddressList: false
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', savedAddress);
      }

      wx.showToast({
        title: editingAddress ? '地址修改成功' : '地址添加成功',
        icon: 'success'
      });
    },

    /**
     * 删除地址
     */
    deleteAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (!address) return;

      wx.showModal({
        title: '确认删除',
        content: `确定要删除地址"${address.name} ${address.detail}"吗？`,
        success: (res) => {
          if (res.confirm) {
            let newAddresses = this.data.savedAddresses.filter(addr => addr.id !== addressId);

            // 如果删除的是当前选中的地址，重新选择默认地址
            let newSelectedAddress = this.data.selectedAddress;
            if (this.data.selectedAddress && this.data.selectedAddress.id === addressId) {
              newSelectedAddress = newAddresses.find(addr => addr.isDefault) || newAddresses[0] || null;
            }

            this.setData({
              savedAddresses: newAddresses,
              selectedAddress: newSelectedAddress
            });

            // 保存到本地存储
            this.saveAddressesToStorage();

            // 通知父组件地址变化
            this.triggerEvent('addresschange', newSelectedAddress);

            wx.showToast({
              title: '地址删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 设置默认地址
     */
    setDefaultAddress(e) {
      const addressId = e.currentTarget.dataset.id;

      const newAddresses = this.data.savedAddresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }));

      const defaultAddress = newAddresses.find(addr => addr.id === addressId);

      this.setData({
        savedAddresses: newAddresses,
        selectedAddress: defaultAddress,
        showAddressList: false
      });

      // 保存到本地存储
      this.saveAddressesToStorage();

      // 通知父组件地址变化
      this.triggerEvent('addresschange', defaultAddress);

      wx.showToast({
        title: '默认地址设置成功',
        icon: 'success'
      });
    },

    /**
     * 阻止事件冒泡
     */
    preventBubble(e) {
      // 阻止事件冒泡，防止触发父级的点击事件
      return false;
    },

    /**
     * 阻止触摸移动事件
     */
    preventTouchMove(e) {
      // 阻止触摸移动事件，防止背景滚动
      return false;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放到页面节点树后执行
      this.loadUserAddress();
    }
  }
});
