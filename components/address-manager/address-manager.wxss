/* 地址管理组件样式 */
.address-manager {
  width: 100%;
}

/* 地址显示区域 */
.address-display {
  padding: 24rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.address-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.address-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.address-placeholder {
  font-size: 28rpx;
  color: #999;
}

.address-arrow {
  font-size: 24rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 地址和时间选择弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  padding: 20rpx;
  box-sizing: border-box;
}

.address-modal .modal-content {
  background: #fff;
  border-radius: 24rpx;
  width: calc(100% - 40rpx);
  max-width: 600rpx;
  max-height: calc(100vh - 80rpx);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1002;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  margin: 0 auto;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 20rpx;
}

/* 地址列表样式 */
.address-list {
  max-height: 500rpx;
  padding: 20rpx 30rpx;
}

.address-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.address-item.selected {
  background: rgba(255, 74, 74, 0.05);
}

.address-item:last-child {
  border-bottom: none;
}

.address-item-content {
  flex: 1;
  margin-right: 20rpx;
}

.address-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 20rpx;
}

.address-phone {
  font-size: 26rpx;
  color: #666;
}

.address-default {
  background: #ff4a4a;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  line-height: 1;
}

.address-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  position: relative;
  z-index: 1003;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
  position: relative;
  z-index: 1004;
}

.action-btn.edit {
  background: #007aff;
  color: #fff;
}

.action-btn.edit-legacy {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
  font-size: 22rpx;
}

.action-btn.delete {
  background: #ff4a4a;
  color: #fff;
}

.action-btn.default {
  background: #f5f5f5;
  color: #333;
  border: 1rpx solid #ddd;
}

.empty-address {
  padding: 80rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.address-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.add-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: #007aff;
  border-radius: 12rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
}

.add-address-btn-legacy {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 12rpx;
  font-size: 24rpx;
  border: 1rpx solid #ddd;
  flex: 1;
}

.add-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 地址表单样式 */
.address-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1010;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: rgba(0, 0, 0, 0.6);
  padding: 20rpx;
  box-sizing: border-box;
  padding-top: 40rpx;
}

.form-content {
  background: #fff;
  border-radius: 24rpx;
  width: calc(100% - 40rpx);
  max-width: 600rpx;
  height: calc(100vh - 80rpx);
  max-height: calc(100vh - 80rpx);
  min-height: 500rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1011;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

.form-scroll {
  flex: 1;
  padding: 15rpx 30rpx 20rpx 30rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  min-height: 0;
  height: 0;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-group:last-child {
  margin-bottom: 15rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.form-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #007aff;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.location-icon {
  font-size: 20rpx;
}

.form-input {
  width: 100%;
  padding: 16rpx 18rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007aff;
}

.form-textarea {
  width: 100%;
  min-height: 80rpx;
  padding: 16rpx 18rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  resize: none;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #007aff;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 18rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fff;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-placeholder {
  font-size: 28rpx;
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #ccc;
}

.checkbox-group {
  margin-top: 10rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.form-footer {
  padding: 20rpx 30rpx 24rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.save-btn {
  width: 100%;
  padding: 24rpx;
  background: #007aff;
  color: #fff;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 地图选择器样式 */
.map-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  padding: 20rpx;
  box-sizing: border-box;
}

.map-modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;
}

.map-content {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 10001;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  margin: 0 auto;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.map-modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 20rpx;
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  z-index: 1;
  background: #f5f5f5;
}

.location-map {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.map-center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10002;
  pointer-events: none;
}

.center-cross {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

.center-cross::before,
.center-cross::after {
  content: "";
  position: absolute;
  background: #ff4a4a;
  border-radius: 2rpx;
}

.center-cross::before {
  width: 40rpx;
  height: 4rpx;
  top: 18rpx;
  left: 0;
}

.center-cross::after {
  width: 4rpx;
  height: 40rpx;
  top: 0;
  left: 18rpx;
}

.map-tip {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10002;
  pointer-events: none;
}

.tip-text {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

.map-footer {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.map-cancel-btn,
.map-confirm-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.map-cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

.map-confirm-btn {
  background: #007aff;
  color: #fff;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .address-modal,
  .address-form-modal,
  .map-selector-modal {
    padding: 15rpx 10rpx;
  }

  .address-modal .modal-content,
  .form-content,
  .map-content {
    width: calc(100% - 20rpx);
    max-height: calc(100vh - 60rpx);
  }

  .address-form-modal {
    padding-top: 20rpx;
  }

  .form-content {
    height: calc(100vh - 60rpx);
    max-height: calc(100vh - 60rpx);
    min-height: 500rpx;
  }

  .form-header {
    padding: 20rpx 24rpx;
  }

  .form-scroll {
    padding: 10rpx 24rpx 15rpx 24rpx;
    height: 0;
  }

  .form-group {
    margin-bottom: 20rpx;
  }

  .form-group:last-child {
    margin-bottom: 10rpx;
  }

  .form-label {
    margin-bottom: 8rpx;
  }

  .form-label-row {
    margin-bottom: 8rpx;
  }

  .form-footer {
    padding: 16rpx 24rpx 20rpx 24rpx;
  }

  .map-content {
    height: calc(100vh - 100rpx);
  }
}
