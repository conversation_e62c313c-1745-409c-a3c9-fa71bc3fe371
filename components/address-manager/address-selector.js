// 稳定的地址选择组件
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },

  data: {
    provinces: ['广东省'],
    cities: ['广州市'],
    districts: [
      '越秀区', '荔湾区', '海珠区', '天河区', '白云区', '黄埔区',
      '番禺区', '花都区', '南沙区', '从化区', '增城区'
    ],
    selectedProvince: '广东省',
    selectedCity: '广州市',
    selectedDistrict: '天河区'
  },

  methods: {
    onProvinceChange(e) {
      const province = this.data.provinces[e.detail.value];
      this.setData({
        selectedProvince: province
      });
    },

    onCityChange(e) {
      const city = this.data.cities[e.detail.value];
      this.setData({
        selectedCity: city
      });
    },

    onDistrictChange(e) {
      const district = this.data.districts[e.detail.value];
      this.setData({
        selectedDistrict: district
      });
    },

    confirm() {
      const result = {
        province: this.data.selectedProvince,
        city: this.data.selectedCity,
        district: this.data.selectedDistrict
      };
      
      this.triggerEvent('confirm', result);
      this.close();
    },

    close() {
      this.triggerEvent('close');
    }
  }
});
