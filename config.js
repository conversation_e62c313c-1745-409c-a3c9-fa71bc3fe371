module.exports = async () => ({
  /** 地图的key */
  key: 'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23',
  /** 应用标识 */
  referer: '乙禾素',
  /** 主题色 */
  theme: '#FF0000',
  /** 是否展示智能填写 */
  aiInput: true,
  /** 保存地址回调方式：使用 onClose 回调函数 */
  onClose: async (data) => {
    try {
      console.log('插件回调数据:', data);

      // 获取当前页面实例
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      console.log('当前页面:', currentPage);

      // 如果当前页面有地址管理组件，调用其保存方法
      if (currentPage && currentPage.selectComponent) {
        const addressManager = currentPage.selectComponent('#address-manager');
        console.log('地址管理组件:', addressManager);

        if (addressManager && addressManager.savePluginAddress) {
          await addressManager.savePluginAddress(data);
          console.log('地址保存成功');
        } else {
          console.warn('未找到地址管理组件或保存方法');
          // 如果找不到组件，尝试直接保存到本地存储
          const savedAddresses = wx.getStorageSync('savedAddresses') || [];
          const newAddress = {
            id: Date.now().toString(),
            name: data.receiver,
            phone: data.phone,
            province: data.area && data.area[0] ? data.area[0].name : '广东省',
            city: data.area && data.area[1] ? data.area[1].name : '广州市',
            district: data.area && data.area[2] ? data.area[2].name : '黄埔区',
            detail: data.address,
            label: data.label || '',
            isDefault: data.default || savedAddresses.length === 0,
            latitude: null,
            longitude: null
          };

          // 如果设置为默认地址，先取消其他地址的默认状态
          if (newAddress.isDefault) {
            savedAddresses.forEach(addr => addr.isDefault = false);
          }

          savedAddresses.push(newAddress);
          wx.setStorageSync('savedAddresses', savedAddresses);
          console.log('地址已保存到本地存储');
        }
      }

      wx.showToast({
        title: '地址保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('保存地址失败:', error);
      wx.showToast({
        title: '保存地址失败',
        icon: 'none'
      });
    }
  }
});