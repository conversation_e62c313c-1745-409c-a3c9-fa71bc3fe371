# 微信小程序地图地址输入服务插件集成说明

## 概述

本项目已成功集成微信小程序地图地址输入服务插件（v1.0.2），为用户提供智能地址输入、地址选择、智能地址解析、微信地址导入等功能。

## 已完成的修改

### 1. 插件配置 (app.json)
- 已配置地图地址输入服务插件
- 插件 appId: `wx57d7ae552cbb3084`
- 版本: `1.0.2`
- 导出配置文件: `config.js`

### 2. 插件配置文件 (config.js)
- 配置腾讯地图API密钥
- 设置应用标识为"乙禾素"
- 启用智能填写功能
- 配置 onClose 回调函数处理地址保存

### 3. 地址管理组件 (components/address-manager/)
#### 新增功能：
- `addAddressWithPlugin()` - 使用插件新增地址
- `editAddressWithPlugin()` - 使用插件编辑地址
- `savePluginAddress()` - 保存插件返回的地址数据

#### 界面更新：
- 新增地址按钮：使用插件进行地址输入
- 手动添加按钮：保留原有的手动输入功能
- 编辑按钮：使用插件进行地址编辑
- 手动编辑按钮：保留原有的手动编辑功能

### 4. 外卖页面 (pages/delivery/)
- 为地址管理组件添加 ID，便于插件回调时定位
- 保持原有的地址变化事件处理

## 使用方式

### 用户操作流程

1. **新增地址**
   - 点击"新增地址"按钮（蓝色）
   - 跳转到插件页面进行地址输入
   - 支持智能地址解析、地图选择等功能
   - 保存后自动返回并更新地址列表

2. **编辑地址**
   - 在地址列表中点击"编辑"按钮
   - 跳转到插件页面，预填充现有地址信息
   - 修改后保存并返回

3. **备用功能**
   - "手动添加"和"手动编辑"按钮保留原有功能
   - 适用于插件无法使用的情况

### 插件功能特性

1. **智能地址输入**
   - 支持地址智能解析
   - 自动补全地址信息
   - 地图选择位置

2. **微信地址导入**
   - 可导入微信收货地址
   - 快速填充地址信息

3. **地址验证**
   - 自动验证地址格式
   - 确保地址信息完整性

## 技术实现

### 插件调用方式

```javascript
// 新增地址
wx.navigateTo({ 
  url: 'plugin://address-form/index' 
});

// 编辑地址
const editInfo = {
  receiver: '收件人',
  phone: '手机号',
  area: [
    { name: '省份', id: '' },
    { name: '城市', id: '' },
    { name: '区县', id: '' }
  ],
  address: '详细地址',
  label: '地址标签',
  default: true
};

wx.navigateTo({ 
  url: `plugin://address-form/index?edit=${JSON.stringify(editInfo)}` 
});
```

### 数据格式转换

插件返回的数据格式会自动转换为本地存储格式：

```javascript
// 插件格式 -> 本地格式
{
  receiver: '张三',           // -> name
  phone: '13800138000',       // -> phone
  area: [                     // -> province, city, district
    { name: '广东省' },
    { name: '广州市' },
    { name: '天河区' }
  ],
  address: '详细地址',        // -> detail
  label: '家',               // -> label
  default: true              // -> isDefault
}
```

## 测试

项目包含测试文件用于验证插件功能：
- `test-plugin-address.js` - 测试页面逻辑
- `test-plugin-address.wxml` - 测试页面模板
- `test-plugin-address.wxss` - 测试页面样式

## 注意事项

1. **权限要求**
   - 需要用户授权位置信息
   - 确保小程序已添加插件并通过审核

2. **兼容性**
   - 保留原有手动输入功能作为备用
   - 插件无法使用时可降级到手动模式

3. **数据同步**
   - 插件保存的地址会自动同步到本地存储
   - 支持设置默认地址
   - 自动更新当前选中地址

## 后续优化建议

1. **经纬度获取**
   - 可考虑在插件回调后获取地址的经纬度信息
   - 用于配送距离计算等功能

2. **地址验证**
   - 可添加地址有效性验证
   - 确保配送范围内的地址

3. **用户体验**
   - 可添加地址使用频率统计
   - 智能推荐常用地址
