{"pages": ["pages/index/index", "pages/reserve/reserve", "pages/index/article/article", "pages/topic/topic", "pages/user/user", "pages/user/details", "pages/user/about", "pages/user/feedback", "pages/user/record", "pages/user/coupon/coupon", "pages/user/order/order_list", "pages/user/order/order_detail", "pages/booking/booking", "pages/booking_employee/booking_employee", "pages/menu/menu", "pages/booking_business/booking_business", "pages/shopping/shopping", "pages/delivery/delivery", "pages/phoneAuth/phoneAuth", "pages/booking_business_edit/booking_business_edit", "pages/booking_business_pay/booking_business_pay", "pages/scan_redirect/scan_redirect", "pages/interactive_ordering/interactive_ordering", "pages/enterprise/user/user", "pages/enterprise/account/account", "pages/enterprise/statistic/statistic", "pages/admin/statistic/statistic", "pages/admin/scan/scan"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "WeChat", "navigationBarTextStyle": "black"}, "tabBar": {"color": "#9BABBA", "selectedColor": "#495056", "borderStyle": "white", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "images/tar-home.png", "selectedIconPath": "images/tar-home-on.png", "text": "首页"}, {"pagePath": "pages/topic/topic", "iconPath": "images/tar-topic.png", "selectedIconPath": "images/tar-topic-on.png", "text": "我要预约"}, {"pagePath": "pages/reserve/reserve", "iconPath": "images/tar-li.png", "selectedIconPath": "images/tar-li-on.png", "text": "订单记录"}, {"pagePath": "pages/user/user", "iconPath": "images/tar-person.png", "selectedIconPath": "images/tar-person-on.png", "text": "我的信息"}]}, "networkTimeout": {"request": 10000, "connectSocket": 10000, "uploadFile": 10000, "downloadFile": 10000}, "navigateToMiniProgramAppIdList": ["wxc58a034f610866c5"], "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于小程序定位"}}, "plugins": {"address-form": {"version": "1.0.2", "provider": "wx57d7ae552cbb3084", "export": "config.js"}, "chooseLocation": {"version": "1.0.12", "provider": "wx76a9a06e5b4e693e"}}, "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}